# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import log
from weeeTest.utils import jmespath

from wms.test_dir.api.wms import header


class Picking(weeeTest.TestCase):
    """
    拣货相关方法
    """

    def enter_module(self, warehouse_number, module_name, action=1):
        url = "/wms/common/record/module/log"
        body = {
            "warehouse_number": warehouse_number,
            "module_name": module_name,
            "action": action
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def query_user_task(self, cart_no, warehouse_number, storage_type, picking_channel=0):
        """
        获取user名下的拣货任务
        """
        url = "/wms/orderPick/task/user"
        body = {
                "cart_no": cart_no,
                "warehouse_number": warehouse_number,
                "storage_type": storage_type,
                "picking_channel": picking_channel
            }
        self.post(url, headers=header, json=body)
        # 此处不断言接口是否请求成功，需要用接口的messageId 做业务逻辑判断
        return self.response
    
    def create_picking_task(self, cart_no, warehouse_number, storage_type, picking_channel=0):
        """
        创建拣货任务
        :param cart_no: 库位编号
        :param warehouse_number: 仓库号
        :param storage_type: 存储类型, 1:常温,包含D,MO,酒类 2:冷藏,包含Fresh 3:冷冻Frozen 6:Multiple, Mof, 7:Frozen, Dry-Ice
        :param picking_channel: 作业通道, 0:picking 1:mailOrder 2:alcohol
        :return:
        """
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number,
            "storage_type": storage_type,
            "picking_channel": picking_channel
        }
        self.post("/wms/orderPick/createPickingTask", headers=header, json=body)
        # 此处不断言接口是否请求成功，需要用接口的messageId 做业务逻辑判断
        result = jmespath(self.response, "success")
        if result:
            log.info(f'创建拣货任务成功。')
        else:
            log.error(f'创建拣货任务失败, Error response:{self.response}')
        return self.response

    def adjust_location(self, warehouse_number, tote_no, location_no, item_number, order_id, picking_quantity,
                        picking_type, picking_task_id):
        """
        库存调整
        :param warehouse_number:
        :param tote_no:
        :param location_no:
        :param item_number:
        :param order_id:
        :param picking_quantity:
        :param picking_type:
        :param picking_task_id:
        :return:
        """
        url = "/wms/orderPick/adjustLocation"
        body = {
            "warehouse_number": warehouse_number,
            "tote_no": tote_no,
            "location_no": location_no,
            "item_number": item_number,
            "order_id": order_id,
            "picking_quantity": picking_quantity,
            "picking_type": picking_type,
            "picking_task_id": picking_task_id,
            "long_press": 1,
            "scanLocationDtm": 1661243552,
            "scanUpcDtm": 1661243556
        }
        self.post(url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def picking_task_finish(self, picking_task_id, picking_type, packing_line, warehouse_number, picking_task_status, picking_channel=0):
        """
        完成拣货任务
        :param picking_task_id:
        :param picking_type:
        :param packing_line:
        :param warehouse_number:
        :param picking_task_status:
        :return:
        """
        url = "/wms/orderPick/pickingTaskFinish"
        body = {
            "picking_task_id": picking_task_id,
            "picking_type": picking_type,
            "packing_line": packing_line,
            "picking_task_status": picking_task_status,
            "warehouse_number": warehouse_number,
            "picking_channel": picking_channel
        }
        self.post(url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def put_wall_scan_cart(self, cart_no, warehouse_number):
        """
        防置扫描车
        :param cart_no:
        :param warehouse_number:
        :return:
        """
        url = "/wms/putwall/cart"
        body = {
            "cart_no": cart_no,
            "warehouse_number": warehouse_number
        }
        self.post(url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def bind_tote_number(self, picking_task_id, tote_no, order_id, shipping_type, warehouse_number):
        """
        绑定拣货框
        :param picking_task_id:
        :param tote_no:
        :param order_id:
        :param shipping_type:
        :param warehouse_number:
        :return:
        """
        url = "/wms/orderPick/bindToteNumber"
        body = {
            "picking_task_id": picking_task_id,
            "tote_no": tote_no,
            "order_id": order_id,
            "shipping_type": shipping_type,
            "warehouse_number": warehouse_number
        }
        self.post(url, headers=header, json=body)
        assert self.response['success'] is True, f"{url}接口请求失败,详情：{self.response}"
        return self.response

    def bind_tote_number_1(self, box_size, tote_no, order_id, warehouse_number):
        """

        :param box_size:
        :param tote_no:
        :param order_id:
        :param warehouse_number:
        :return:
        """
        body = {
            "box_size": box_size,
            "tote_no": tote_no,
            "order_id": order_id,
            "warehouse_number": warehouse_number
        }
        self.post("/wms/putwall/order/tote", headers=header, json=body)

    def pick_items(self, picking_task_id, warehouse_number):
        """
        处理拣货任务组
        :param picking_task_id:
        :param warehouse_number:
        :return:
        """
        body = {
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse_number
        }
        self.post("/wms/putwall/item", headers=header, json=body)
        result = jmespath(self.response, "success")
        if result:
            log.info(f'处理拣货任务组成功。')
        else:
            log.error(f'处理拣货任务组失败, Error response:{self.response}')
        return self.response

    def sorting_items(self, item_number, order_id, picking_task_id, sorting_quantity):
        """
        SORTING ITEMS
        :param item_number:
        :param order_id:
        :param picking_task_id:
        :param sorting_quantity:
        :return:
        """
        body = {
            "item_number": item_number,
            "order_id": order_id,
            "picking_task_id": picking_task_id,
            "sorting_quantity": sorting_quantity,
        }
        self.post("/wms/putwall/item", headers=header, json=body)
        return self.response

    # 再次调用/putwall/cart，返回packing line，结束任务
    def sorting(self, action, item_number, order_id, picking_task_id, sorting_quantity):
        body = {
            "action": 1,
            "item_number": item_number,
            "picking_task_id": picking_task_id,
            "order_id": order_id,
            "sorting_quantity": sorting_quantity,
        }
        self.post("/wms/putwall/sorting", headers=header, json=body)
        return self.response

    def task_finish(self, cart_no, packing_line, picking_task_id, warehouse_number):
        body = {
            "cart_no": cart_no,
            "packing_line": packing_line,
            "picking_task_id": picking_task_id,
            "warehouse_number": warehouse_number,
        }
        self.post("/wms/putwall/task", headers=header, json=body)
        return self.response

    def outOfStock(self,warehouse_number,tote_no,location_no,item_number,picking_quantity,order_id,picking_type,picking_task_id):
        body = {
                "warehouse_number": warehouse_number,
                "tote_no": tote_no,
                "location_no": location_no,
                "item_number": item_number,
                "picking_quantity":picking_quantity,
                "order_id": order_id,
                "picking_type": picking_type,
                "picking_task_id": picking_task_id
            }
        self.post("/wms/orderPick/outOfStock", headers=header, json=body)
        return self.response
