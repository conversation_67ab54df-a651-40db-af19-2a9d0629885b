# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import weeeTest
from weeeTest import log

from weeeTest.utils import jmespath

from wms.test_dir.api.wms.wms import header


class ReplenishAPI(weeeTest.TestCase):
    warehouse = "33"

    def check_tote(self, tote_no):
        """
        扫描待补单的tote
        """
        url = "/wms/replenish/checkTote"
        body = {
            "order_tote_no": tote_no,
            "warehouse_number": self.warehouse,
            "storage_type": "1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def check_slot(self, slot_no, order_id):
        """
        扫描可用的slot
        """
        url = "/wms/replenish/checkSlot"
        body = {
            "slot_no": slot_no,
            "order_id": order_id,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def bind_slot(self, tote_no, slot_no):
        """
        tote与slot绑定
        """
        url = "/wms/replenish/bindSlot"
        body = {
            "order_tote_no": tote_no,
            "slot_no": slot_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response['body']['itemList'][0]['needPreoccupyList'][0]['batch_no']


    def get_pick_list(self):
        """
        获取待拣货的列表
        """
        url = "/wms/replenish/pickList"
        body = {
            "warehouse_number": self.warehouse,
            "preempted": True,
            "storage_type": "1"
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        result = jmespath(self.response, "body.waitPickingList")
        if result:
            log.info(f'补单预占成功')
        else:
            raise Exception(f'补单预占失败')
        return self.response["body"]["waitPickingList"]

    def scan_hot_tote(self, hot_tote):
        """
        扫描拣货tote
        """
        url = "/wms/replenish/checkHotTote"
        body = {
            "hot_pick_toto_no": hot_tote,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def update_as_container_status(self,container):
        """
        更新as container状态
        """
        url = "/wms/automation/v1/updateDestinationStatus"
        body = {
            "moduleName": "replenish",
            "warehouseNumber": self.warehouse,
            "kpiUpdateRequestParam": {
                "destinationId": container,
                "destinationStatus": "Empty"
            }
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def scan_location(self, location_no, item_number):
        """
        扫描拣货库位，进入拣货详情页
        """
        url = "/wms/replenish/pickDetail"
        body = {
            "bin_no": location_no,
            "warehouse_number": self.warehouse,
            "item_number": item_number
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def pick_confirm(self, pick_list, hot_tote):
        """
        拣货数据确认提交
        """
        url = "/wms/replenish/pickConfirm"
        body = {
            "bin_no": "",
            "hot_pick_toto_no": hot_tote,
            "item_number": "",
            "pick_quantity": "",
            "warehouse_number": self.warehouse
        }
        for p in pick_list:
            body["bin_no"] = p["location_no"]
            body["item_number"] = p["item_number"]
            body["pick_quantity"] = p["need_pick_quantity"]
            self.scan_location(p["location_no"], p["item_number"])
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def pick_oos_confirm(self, pick_list, hot_tote):
        """
        拣货数据确认提交
        """
        url = "/wms/replenish/pickConfirm"
        body = {
            "bin_no": "",
            "hot_pick_toto_no": hot_tote,
            "item_number": "",
            "pick_quantity": "",
            "warehouse_number": self.warehouse
        }
        for p in pick_list:
            body["bin_no"] = p["location_no"]
            body["item_number"] = p["item_number"]
            body["pick_quantity"] = 0
            self.scan_location(p["location_no"], p["item_number"])
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def as_pick_confirm(self, pick_list, hot_tote):
        """
        as拣货数据确认提交
        """
        url = "/wms/replenish/pickConfirm"
        body = {
            "bin_no": "",
            "hot_pick_toto_no": hot_tote,
            "item_number": "",
            "inventory_source": "",
            "container_no": "",
            "chute_id": "",
            "pick_list_rec_id": "",
            "warehouse_number": self.warehouse
        }
        for p in pick_list:
            body["bin_no"] = p["chute_id"]
            body["item_number"] = p["item_number"]
            body["inventory_source"] = p["inventory_source"]
            body["container_no"] = p["chute_id"]
            body["chute_id"] = p["chute_id"]
            body["pick_list_rec_id"] = p["pick_list_rec_id"]
            self.post(url=url, headers=header, json=body)
            assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def query_hot_pick_tote(self):
        """
        获取待check in的pick tote列表
        """
        url = "/wms/replenish/hot_picking_totes/query"
        body = {
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def scan_hot_pick_tote(self, hot_tote):
        """
        扫描需check in的pick tote
        """
        url = "/wms/replenish/checkHotTote"
        body = {
            "hot_pick_toto_no": hot_tote,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def get_checkin_list(self, hot_tote):
        """
        获取待checkin的item列表
        """
        url = "/wms/replenish/itemCheckInList"
        body = {
            "hot_pick_toto_no": hot_tote,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def enter_checkin_detail(self, upc, hot_tote):
        """
        扫描UPC进入check in details页面
        response:
        {
            "messageId": "10000",
            "success": true,
            "body": {
                "item_number": "96972",
                "slot_no": "R05F05",
                "image": "https://img06.weeecdn.com/product/image/009/867/5FE1F138A936F355.jpeg",
                "name": "【家乡味】有机黑米 16oz 中英文版本混发",
                "ename": "[Big Green] Organic Black Rice 454g",
                "tote_no": null,
                "quantity": null,
                "worker_id": null
            }
        }
        """
        url = "/wms/replenish/itemCheckInDetail"
        body = {
            "hot_pick_toto_no": hot_tote,
            "upc_code": upc,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def item_checkin_confirm(self, hot_tote, slot_no, item_list):
        """
        扫描UPC进入check in details页面
        """
        url = "/wms/replenish/itemCheckInConfirm"
        for item in item_list:
            for q in range(int(item["quantity"])):
                body = {
                    "slot_no": slot_no,
                    "hot_pick_toto_no": hot_tote,
                    "item_number": item["item_number"],
                    "upc_code": item["upc"],
                    "warehouse_number": self.warehouse
                }
                self.post(url=url, headers=header, json=body)
                assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"

    def finish_replenish(self, slot_no, tote_no):
        """
        check in 完成调用finish接口
        """
        url = "/wms/replenish/finishReplenished"
        body = {
            "slot_no": slot_no,
            "hot_pick_toto_no": tote_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def get_user_replenish_list(self, storage_type):
        """
        获取用户未做完的补单任务
        """
        url = "/wms/replenish/queryMyReplenishList"
        body = {
            "warehouse_number": self.warehouse,
            "storage_type": storage_type
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def checkout_force_complete(self, tote_no, slot_no):
        """
        强制结束补单流程
        """
        url = "/wms/replenish/forceFinishReplenished"
        body = {
            "order_tote_no": tote_no,
            "slot_no": slot_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response

    def checkout_unbind(self, tote_no, slot_no):
        """
        解绑订单tote与slot的关系，初始化补单状态
        """
        url = "/wms/replenish/unbindSlot"
        body = {
            "order_tote_no": tote_no,
            "slot_no": slot_no,
            "warehouse_number": self.warehouse
        }
        self.post(url=url, headers=header, json=body)
        assert self.response['success'] == True, f"{url}接口请求失败,详情：{json.dumps(self.response)}"
        return self.response
