# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  poreceive_process.py
@Description    :  
@CreateTime     :  2025/6/21 16:06
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/6/21 16:06
"""
from time import sleep

from jsonpath import jsonpath

from wms.test_dir.api.wms.wms import wms


def get_available_pallet(warehouse_number,location_type):
    """获取可用的托盘"""
    datalist = wms.common_api.get_avaiable_location(warehouse_number=warehouse_number,
                                                    location_type=location_type, flag=0)
    return jsonpath(datalist, '$[0].location_no')[0]

def central_receive(warehouse_number, po_number,user):
    # 验证PO信息
    wms.central_receive.central_query_po(warehouse_number, po_number)

    # 获取PO单下的商品信息
    items = wms.central_receive.receive_list(warehouse_number, po_number)
    if not items:
        return

    # 提取商品编号列表
    item_numbers = [data['itemNo'] for data in items]

    # 获取每个商品的详情并创建收货任务
    for i, item_number in enumerate(item_numbers):
        # 获取商品详情信息
        item_detail = wms.central_receive.receive_detail(warehouse_number, po_number, item_number)
        pieces_per_pack = item_detail['pieces_per_pack']

        # 获取商品保质期属性信息
        item_info = wms.common_api.query_item_infos(
            warehouse_number, item_number,
            attributes=[39], in_user=user
        )

        # 设置过期日期
        expire_date = ""
        if item_info[0]["expiration_control"] == "Y":
            expire_date = wms.util.get_special_date(days=365)

        # 创建收货任务
        wms.central_receive.create_task(
            warehouse_number, po_number,
            item_number, pieces_per_pack, expire_date
        )

def app_receive(warehouse_number, po_number):
    # 模拟用户进入Receive模块(逻辑上需要比较用户进入模块与业务单历史作业模块相同)
    wms.common_api.record_moudle_log(warehouse_number, 'receive')
    # APP端——检查PO信息
    wms.central_receive.query_po(po_number, warehouse_number, 2)

    # APP端——获取PO单下的receive task信息
    result = wms.central_receive.query_receive_task_list(po_number, 10, warehouse_number)
    if not result:
        return

    # 验证任务状态
    task_status = result['taskVoList'][0]["status"]
    assert task_status == 10, "生成的receive_task状态错误"

    # 提取收货任务信息
    receive_tasks = {task['receive_task_no']: task['item_number']
                     for task in result['taskVoList']
                     if task['receive_task_no']}

    # APP端——扫描并处理每个receive task
    # processed_tasks = {}
    for task_no in receive_tasks:
        # 扫描receive task
        task_result = wms.central_receive.query_receive_task_list(
            po_number, task_status, warehouse_number,
            upc_code=task_no, flag=False
        )
        if not task_result:
            continue

        # APP端——进入收货详情页面
        wms.central_receive.app_receive_detail(po_number, receive_tasks[task_no])
        # processed_tasks[task_no] = receive_tasks[task_no]
        po_detail = wms.common_api.central_query_po_detail(
            warehouse_number=warehouse_number,
            po_number=po_number,
            item_number_or_upc=receive_tasks[task_no]
        )
        qty_to_receive = po_detail['items'][0]['poUnitQty']

        # 收货前检查库存
        pre_inventory = wms.adjust_api.query_location_inv_list(
            warehouse_number=warehouse_number,
            item_number=receive_tasks[task_no],
            location_no='RECG0001'
        )
        pre_qty = 0 if pre_inventory == [] else pre_inventory[0]['quantity']

        # 执行收货操作
        # 先检查收货数据
        wms.central_receive.central_receive_confirm(
            warehouse_number, qty_to_receive, po_number,
            receive_tasks[task_no], task_no
        )

        # 最终收货确认
        wms.central_receive.central_receive_confirm(
            warehouse_number, qty_to_receive, po_number,
            receive_tasks[task_no], task_no, only_check=False
        )
        # 模拟用户退出Receive模块
        wms.common_api.record_moudle_log(warehouse_number, 'receive', action=0)

        # 收货后验证
        # 1. 验证收货数量记录
        post_po_detail = wms.common_api.central_query_po_detail(
            warehouse_number=warehouse_number,
            po_number=po_number,
            item_number_or_upc=receive_tasks[task_no]
        )
        received_qty = post_po_detail['items'][0]['receiveUnitQty']
        assert received_qty == qty_to_receive, f"预期收货数量{qty_to_receive}，实际收货数量{received_qty}"

        # 2. 验证库存增加
        post_inventory = wms.adjust_api.query_location_inv_list(
            warehouse_number=warehouse_number,
            item_number=receive_tasks[task_no],
            location_no='RECG0001'
        )
        assert post_inventory[0]['quantity'] == pre_qty + qty_to_receive, f"预期库存{pre_qty + qty_to_receive}，实际库存{post_inventory[0]['quantity']}"

def putaway_operation(putaway_warehouse_no, po_number, in_user, item_number, storage_type=1, type=0):
    '''
    type: 默认0为普通；1为as；2为geek

    '''
    # 模拟用户进入putaway模块
    wms.common_api.record_moudle_log(putaway_warehouse_no, 'put_away')

    # 获取当前用户有无已领取未上架的任务,# 若有未完成的任务先释放掉
    undo_info = wms.repack_api.queryUndoTaskList(warehouse_number=putaway_warehouse_no, user_id=in_user, po_number="")["body"]
    if undo_info.get("task_id"):
        wms.repack_api.partialComplete(putaway_warehouse_no, undo_info.get("task_id"),in_user)

    # 获取可用的托盘
    pallet_no = get_available_pallet(warehouse_number=putaway_warehouse_no, location_type=38)

    # 检查托盘
    wms.put_away.check_Pallet(putaway_warehouse_no, pallet_no)

    # 获取待上架的MLPN标签
    labels = wms.repack_api.todoList(putaway_warehouse_no, po_number, storage_type, in_user)
    assert labels['body'] != [],"无上架任务"
    label_nos = jsonpath(labels, "$..label_no")

    # 扫描待上架的MLPN并获取任务ID
    task_ids = []
    for label in label_nos:
        result = wms.put_away.scan_And_Check_Task(putaway_warehouse_no, label, storage_type)
        task_ids.append(jsonpath(result, '$[0].task_id')[0])

    # 确认并分配上架任务
    wms.put_away.confirm_And_Schedule_Tasks(putaway_warehouse_no, task_ids, pallet_no)

    # 根据上架类型处理不同的上架逻辑
    task_locations = {}
    task_processes = {}

    if type == 1:  # AutoStore商品
        # 同步AutoStore属性
        item_info = wms.common_api.query_item_infos(
            putaway_warehouse_no, item_number,
            attributes=[78], in_user=in_user
        )
        if item_info[0]["as_sku"] == False:
            wms.put_away.update_items_as_attributes(putaway_warehouse_no, item_number)
        # 处理AutoStore上架
        for task_id in task_ids:
            result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
            putawayquantity = jsonpath(result, '$.total_quantity')[0]
            if result['process'] == 1:
                task_locations[task_id] = "AUTOSTORE"
                task_processes[task_id] = 1

    elif type == 2:  # GeekPlus商品
        # 同步GeekPlus属性
        item_info = wms.common_api.query_item_infos(
            putaway_warehouse_no, item_number,
            attributes=[96], in_user=in_user
        )
        if item_info[0]["geekPlus_sku"] == False:
            wms.put_away.update_items_geek_attributes(putaway_warehouse_no, item_number)
        # 处理GeekPlus上架
        for task_id in task_ids:
            result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
            putawayquantity = jsonpath(result, '$.total_quantity')[0]
            if result['process'] == 1:
                task_locations[task_id] = "GEEKPLUS"
                task_processes[task_id] = 1

    else:  # 普通商品
        # 处理普通商品上架
        for task_id in task_ids:
            result = wms.put_away.query_Putaway_Info(task_id, putaway_warehouse_no)
            putawayquantity = jsonpath(result, '$.total_quantity')[0]

            # 获取推荐库位
            recommend_location = result.get('recommend_location')

            # 如果没有推荐库位且没有可用库位列表，跳过此任务
            if not recommend_location and not result.get('locationList'):
                continue

            # 根据process类型选择合适的库位
            # process: 1=OnlyBin, 2=OnlyStock, 3=random, 5=D_RTV
            process = result['process']
            task_processes[task_id] = process

            if process == 1:  # OnlyBin
                location = recommend_location or next((loc['location_no'] for loc in result['locationList']
                                                       if loc['location_type'] == 4), None)
                if location:
                    task_locations[task_id] = location

            elif process == 2:  # OnlyStock
                location = recommend_location or next((loc['location_no'] for loc in result['locationList']
                                                       if loc['location_type'] == 3), None)
                if location:
                    task_locations[task_id] = location

            elif process == 3:  # random
                task_locations[task_id] = result['locationList'][0]['location_no']

            elif process == 5:  # D_RTV
                task_locations[task_id] = 'D_RTV'

    # 获取上架任务详情
    for task_id, location in task_locations.items():
        process = task_processes[task_id]
        result = wms.put_away.query_Task_Confirm_Detail(task_id, location, putaway_warehouse_no, process)

        rec_ids = jsonpath(result, "$.lpnInfoList[*].rec_id")
        warehouse_numbers = jsonpath(result, "$.lpnInfoList[*].warehouse_number")
        item_numbers = jsonpath(result, "$.lpnInfoList[*].item_number")
        lpn_nos = jsonpath(result, "$.lpnInfoList[*].lpn_no")
        original_quantities = jsonpath(result, "$.lpnInfoList[*].original_quantity")

        # 组合结果
        lpnInfoList = []
        for i in range(len(rec_ids)):
            filtered_lpn = {
                "rec_id": rec_ids[i],
                "warehouse_number": warehouse_numbers[i],
                "item_number": item_numbers[i],
                "lpn_no": lpn_nos[i],
                "original_quantity": original_quantities[i]
            }
            lpnInfoList.append(filtered_lpn)

        quantity = result['recommend_lpn_quantity']
        # 收货前检查库存
        pre_inventory = wms.adjust_api.query_location_inv_list(
            warehouse_number=putaway_warehouse_no,
            item_number=item_number,
            location_no=location
        )
        pre_qty = 0 if pre_inventory == [] else pre_inventory[0]['quantity']

        wms.put_away.confirm_Putaway_po(
            item_number,
            location,
            task_id,
            putaway_warehouse_no,
            lpnInfoList,
            quantity
        )
        # 2. 验证库存增加
        post_inventory = wms.adjust_api.query_location_inv_list(
            warehouse_number=putaway_warehouse_no,
            item_number=item_number,
            location_no=location
        )
        assert post_inventory[0]['quantity'] == pre_qty + putawayquantity, f"预期库存{pre_qty + quantity}，实际库存{post_inventory[0]['quantity']}"

    # 模拟用户退出putaway模块
    wms.common_api.record_moudle_log(putaway_warehouse_no, 'put_away', action=0)