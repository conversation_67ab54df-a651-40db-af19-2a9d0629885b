import weeeTest
from weeeTest import jmespath
from jsonpath import jsonpath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.inv.ec_item_api import EcItem
from wms.test_dir.api_case.inv.utils import InvDataUtils
from wms.qa_config import global_data


class TestEcInventory(weeeTest.TestCase):
    # 库存查询接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_inventory(self, sales_org_id=1, zipcode="94501", warehouse_number="25", product_id=9450):
        """
        【102394】查询库存售卖商品可售库存
        """
        # 登录
        wms.wms_login.common_login()
        # 查询可售库存校验
        product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_schedule(self, sales_org_id=1, zipcode="94501", warehouse_number="7", product_id=97078):
        """
        【102673】查询计划售卖商品可售库存
        """
        # 登录
        wms.wms_login.common_login()
        # 查询可售库存校验
        product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv, sales_model="schedule")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_global(self, zipcode="92069", warehouse_number=None, sales_org_id=None):
        """
        【102674】查询Global+商品可售库存
        """
        # 登录
        wms.wms_login.common_login()
        date = wms.util.get_special_date(days=3)
        # 调EC item查询global商品
        res = EcItem().query_categroy_product(zipcode, date, filters='{"delivery_type":"delivery_type_global"}')
        product_id = jmespath(res, "object.contents[0].data.id")
        assert product_id is not None, f"zipcode={zipcode},date={date}下未找到global商品"

        # 查询zipcode所在region
        region_res = wms.ec_inv_api.query_zipcode_region([zipcode])
        zipcode_region_list = jsonpath(region_res, "$.object[0].region_ids[*]")
        assert zipcode_region_list, f"zipcode={zipcode}绑定的Region为空,请检查!"

        # 查询Product绑定的Region
        product_res = wms.ec_inv_api.query_product_region([product_id])
        product_region_list = jsonpath(product_res, "$.object[*].region_id")
        assert product_region_list, f"product_id={product_id}绑定的Region为空"
        assert all(region in zipcode_region_list for region in product_region_list), f"product_id={product_id}在zipcode={zipcode}下不能履约"
        product_inv = jsonpath(product_res, "$.object[*].inventory_infos[*].qty")
        assert product_inv, f"product_id={product_id}在zipcode={zipcode}下无可售库存"

        # 查询可售库存校验
        product_inv_daily = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)
        assert product_inv[0] == product_inv_daily, "查询商品所有天履约获取到的商品库存与查询单日履约获取到的库存不相等"

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv_daily)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_global_fbw(self, sales_org_id=9, zipcode="92069", warehouse_number="25"):
        """
        【102675】查询Global_Fbw商品可售库存
        """
        account, username = wms.wms_login.common_login()
        # 获取有库存的Global fbw商品, 104:Global FBW
        filter_column_rules = [{
            "column": "globalFbwSku",
            "rule": 1,
            "value": "Y"
        }]
        item_list = wms.common_api.query_item_info(warehouse_number, [104], account, filter_column_rules)
        assert item_list, f"{warehouse_number}仓库未查询到global fbw属性商品"
        product_id = item_list[0]["sku"]

        # 查询可售库存校验
        product_inv_daily = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv_daily)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_local_fbw(self, sales_org_id=9, zipcode="92069", warehouse_number="25"):
        """
        【102676】查询Local_Fbw商品可售库存
        """
        # 登录
        wms.wms_login.common_login()
        date = wms.util.get_special_date(days=3)
        # 调EC item查询global商品
        res = EcItem().query_categroy_product(zipcode, date, filters='{"catalogue_num":"bakery06"}', filter_sub_category='bakery')
        product_id = jmespath(res, "object.contents[0].data.id")
        assert product_id is not None, f"zipcode={zipcode},date={date}下未找到bakery商品"

        # 查询可售库存校验
        product_inv_daily = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv_daily,
                                                 sales_model="schedule")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV', 'INV-PRD')
    def test_query_v5_pantry(self, sales_org_id=3, zipcode="98011", warehouse_number="25"):
        """
        【102682】查询pantry商品可售库存
        """
        # 参数设置
        wms.wms_login.common_login()
        date = wms.util.get_special_date(days=3)
        # 调EC item查询pantry商品
        res = EcItem().query_categroy_product(zipcode, date)
        product_id = jmespath(res, "object.contents[0].data.id")
        assert product_id is not None, f"zipcode={zipcode},date={date}下未找到pantry商品"
        # 查询可售售库存校验
        product_inv = InvDataUtils().assert_inventory_daily_sales(sales_org_id, wms.util.get_special_date(days=3), zipcode, product_id)

        # V5接口校验
        InvDataUtils().assert_inventory_query_v5(wms.util.get_special_date(days=3), zipcode, product_id, warehouse_number, product_inv)

    # 客户订单下单接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_inventory(self, product_id=9450, product_type='normal', sales_org_id=1, zipcode="94527"):
        """
        【102556】下单&取消_库存售卖商品
        """
        # 参数设置
        wms.wms_login.common_login()
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_schedule(self, product_id=1745, product_type='normal', sales_org_id=1, zipcode="94527"):
        """
        【102557】下单&取消_计划售卖商品
        """
        # 参数设置
        wms.wms_login.common_login()
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_global(self, product_id=global_data.global_query['product_id'], product_type='seller', sales_org_id=global_data.global_query['sales_org_id'],zipcode=global_data.global_query['zipcode']):
        """
        【102679】下单&取消_global商品
        """
        # 参数设置
        wms.wms_login.common_login()
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_global_fbw(self, product_id=global_data.global_fbw_query['product_id'], product_type='mkpl_fbw', sales_org_id=global_data.global_fbw_query['sales_org_id'], zipcode=global_data.global_fbw_query['zipcode']):
        """
        【102678】下单&取消_global fbw
        """
        # 参数设置
        wms.wms_login.common_login()
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_local_fbw(self, product_id=global_data.local_fbw_query['product_id'], product_type='fbw', sales_org_id=global_data.local_fbw_query['sales_org_id'], zipcode=global_data.local_fbw_query['zipcode']):
        """
        【102677】下单&取消_local fbw
        """
        # 参数设置
        wms.wms_login.common_login()
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_order_pantry(self, product_type='normal', sales_org_id=4, zipcode="98001"):
        """
        【102558】下单&取消_直邮商品
        """
        # 参数设置
        wms.wms_login.common_login()
        res = EcItem().query_categroy_product(zipcode, wms.util.get_special_date(days=3))  # 调EC item查询pantry商品
        product_id = jmespath(res, "object.contents[0].data.id")
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(wms.util.get_special_date(days=3), product_id, product_type, sales_org_id, zipcode)
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(wms.util.get_special_date(days=3), order_id, product_id, product_type, sales_org_id, zipcode)

    # Vendor Return单下单接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_vendor_return_order(self, warehouse_number='48', product_id=105349):
        """
        【103039/103040】Vendor Return单下单/取消订单
        """
        # 参数设置
        wms.wms_login.common_login()  # 登录
        # Vendor Return单下单校验
        order_id = InvDataUtils().assert_create_vendor_return(warehouse_number, product_id, types=40)
        # Vendor Return单取消订单校验
        InvDataUtils().assert_cancel_vendor_return(warehouse_number, product_id, order_id, types=41)

    # 调拨单下单接口
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_transfer_order(self, date=wms.util.get_special_date(days=3), from_warehouse_number="48", product_id=105349, to_warehouse_number="29"):
        """
        【103035/103036】Transfer order下单/取消订单
        """
        # 参数设置
        wms.wms_login.common_login()  # 登录
        # 调拨单下单校验
        order_id = InvDataUtils().assert_create_transfer_order(date, from_warehouse_number, product_id, to_warehouse_number)
        # 调拨单取消订单校验
        InvDataUtils().assert_cancel_transfer_order(date, from_warehouse_number, product_id, to_warehouse_number, order_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

'''
自动化库存数据
库存售卖: 9450  zipcode: 94501
计划售卖: 97078  zipcode: 94501
直邮商品: item查  zipcode: 98011
LocalFBW: 2846410   zipcode: 98208
GlobalFBW: 2846393  zipcode: 92069
Global +: 2846374   zipcode: 92069
'''
