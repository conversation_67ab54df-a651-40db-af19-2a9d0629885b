import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestReserve(weeeTest.TestCase):
    """
     赠品活动相关接口
    """

    def setup_class(self):
        # 登录
        wms.wms_login.common_login()
        self.sales_org_id = global_data.reserve['sales_org_id']
        self.product_id = global_data.reserve['product_id']
        self.reserve_qty = global_data.reserve['reserve_qty']
        self.product_type = global_data.reserve['product_type']
        self.zipcode = global_data.reserve['zipcode']
        self.date = wms.util.get_special_date(days=3)

    # 赠品活动预占库存测试
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_reserve_start(self):
        """
        【102273】创建赠品活动_预占reserve库存
        """
        InvDataUtils().reserve_start(self.product_id, self.reserve_qty, self.sales_org_id)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_create_order_inventory(self):
        """
        【102271/102272】reserve库存_下单&取消
        """
        # 下单校验
        order_id = InvDataUtils().assert_create_order_v5(self.date, self.product_id, self.product_type, self.sales_org_id, self.zipcode,
                                                         inventory_mode="reserve")
        # 取消订单校验
        InvDataUtils().assert_cancel_order_v5(self.date, order_id, self.product_id, self.product_type, self.sales_org_id, self.zipcode,
                                              inventory_mode="reserve")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_reserve_end(self):
        """
        【102274】结束赠品活动_退还reserve库存
        """
        InvDataUtils().reserve_end(self.product_id, self.sales_org_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
# 活动库存预占
# 活动库存下单/取消
# 活动预占结束
