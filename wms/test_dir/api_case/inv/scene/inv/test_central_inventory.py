import weeeTest
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.inv.utils import InvDataUtils


class TestCentralQuery(weeeTest.TestCase):
    """
    Central-INV查询接口
    """
    def setup_class(self):
        self.sales_org_id = 9
        self.zipcode = "92069"
        self.sales_model = "inventory"
        self.region_id = "10"
        self.warehouse_number = "25"
        self.seller_id = 9744
        self.date = wms.util.get_special_date(days=3)
        # 登录
        wms.wms_login.common_login()

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'INV')
    def test_query_zipcode_region(self):
        """
        【】zipcode和region关系查询
        """
        # EC-INV相关接口
        # /ec/inventory/query/local/region接口查询
        InvDataUtils().assert_query_zipcode_region(self.zipcode, self.region_id)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)

