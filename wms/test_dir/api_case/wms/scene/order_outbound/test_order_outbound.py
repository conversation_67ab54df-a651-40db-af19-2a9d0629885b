# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.business.picking_process import general_picking_operate
from wms.test_dir.business.packing_process import packing_process
from wms.test_dir.business.adjust_inv_process import bin_adjust
from wms.test_dir.business.down_order_process import DownOrderProcess
from common.api.order_utils import OrderTestUtils


class TestOrderOutboundProcess(weeeTest.TestCase):
    """
    订单下单-down单-预占-Picking-Packing-Route Check全流程Case
    """

    def setup_class(self):
        self.warehouse_number = global_data.outbound_info["warehouse_number"]
        self.region_id = "43"
        self.region_name = "CN - Test"
        self.user_id, self.user_name = wms.wms_login.common_login()
        self.in_user = f"{self.user_name}({self.user_id})"
        self.order_type_list = global_data.outbound_info["order_type"]
        self.delivery_date = global_data.outbound_info.get("delivery_date", wms.util.get_special_date(days=1))
        self.order_id = []
        self.delivery_id = ""

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_purchase_order(self):
    #     """
    #     下单
    #     """
    #     products = [{"product_id": "95364", "quantity": 1}, {"product_id": "104615", "quantity": 1},{"product_id": "95886", "quantity": 1}]
    #     order_id = OrderTestUtils().create_test_order(products, delivery_date=self.delivery_date)
    #     self.order_id.append(order_id)
    #     print(order_id)

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_manual_down_order(self):
    #     """
    #     normal down order流程
    #     """
    #     DownOrderProcess().manual_pull_order(self.warehouse_number, self.delivery_date, [self.region_id], self.user_id)
    #     for order_id in self.order_id:
    #         order_info = wms.down_order.fpo_order_page(order_id)
    #         assert order_info, f"Customer order:{order_id} does not exist in FPO"
    #         self.delivery_date = order_info[0]["delivery_date"]

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_update_route_info(self):
    #     """
    #     排车下发WMS路线信息
    #     """
    #     delivery_list = wms.down_order.list_delivery([self.region_id], self.delivery_date, self.delivery_date)
    #     assert delivery_list, f"region:{self.region_name}在{self.delivery_date}生成发货批次失败"
    #     self.delivery_id = delivery_list[0]["id"]
    #     invoice_list = wms.down_order.fpo_invoice_page(delivery_id=self.delivery_id)
    #     packing_num = 1
    #     for invoice in invoice_list:
    #         invoice_id = invoice["group_invoice_id"] if invoice["group_invoice_id"] else invoice["invoice_id"]
    #         wms_order_list = wms.down_order.query_invoice_order(invoice_id)
    #         for order in wms_order_list:
    #             orderId = str(order["orderId"])
    #             if order["orderType"] in [2,3,7]:
    #                 if len(orderId) >= 9 and orderId[-2:] in {'02', '03', '07'}:
    #                  orderId = orderId[:-2]
    #             wms.down_order.update_order_route_info([self.delivery_id], self.warehouse_number, invoice_id, packing_num, 9999, orderId)
    #         packing_num += 1

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_ready_for_picking(self):
    #     """
    #     ready_for_picking流程
    #     """
    #     DownOrderProcess().ready_for_picking(self.warehouse_number, self.delivery_date, self.region_name)
    #     region_list = wms.down_order.fpo_region_by_warehouse(self.warehouse_number, self.delivery_date)
    #     region_info =jmespath(region_list, f"[?region_id == `{self.region_id}`]")[0]
    #     assert region_info["ready_for_picking"] == True, f"{self.region_name}在{self.delivery_date} ready for picking失败"


    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    # def test_generate_batch(self):
    #     """
    #     generate_batch流程
    #     """
    #     for order_type in self.order_type_list:
    #         # 查询无库存可预占的订单
    #         oos_info = wms.oos_api.get_order_oos_list_page(self.warehouse_number, self.delivery_date, order_type=order_type)
    #         if oos_info["recordsTotal"] !=0:
    #             oos_info = wms.oos_api.get_order_oos_list_page(self.warehouse_number, self.delivery_date, order_type=order_type, pageSize=oos_info["recordsTotal"])["data"]
    #             for item in oos_info:
    #                 location_no = None
    #                 if item["binList"]:
    #                     location_no = item["binList"][0]
    #                 bin_adjust(item["item_number"], self.warehouse_number,self.in_user, location_no=location_no)
    #         DownOrderProcess().generate_batch_operate(self.warehouse_number, self.delivery_date, order_type)
    #         wms.util.wait(sleep_time=1)

    #     # 查询是否还有未预占成功的订单        
    #     order_list = wms.batch.query_order_info(self.warehouse_number,self.delivery_date, status="0")
    #     assert len(order_list) == 0, f"仓库：{self.warehouse_number},delivery_data:{self.delivery_date},order_type:{order_type} 存在未预占成功的订单"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_normal_picking(self):
        """
        【112950】Normal picking 流程
        """
        for storage_type in ["1", "2", "3"]:
            general_picking_operate(self.warehouse_number, storage_type)


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mailorder_picking(self):
        """
        【112953】Mail Order picking 流程
        """
        warehouse_number = global_data.mo_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=50, picking_channel=1, module_name="mail_order")


    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_oneitem_picking(self):
        """
        【112952】Oneitem picking 流程
        """
        warehouse_number = global_data.one_item_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=52, is_oneitem=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_picking(self):
        """
        【112951】Bulk picking 流程
        """
        general_picking_operate(self.warehouse_number, "1", location_type=73, is_bulk=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_mof_picking(self):
        """
        【112954】MOF picking 流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        general_picking_operate(warehouse_number, "6", location_type=50, picking_channel=1, module_name="mail_order", is_mof=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fbw_picking(self):
        """
        【112955】Fbw picking 流程
        """
        warehouse_number = global_data.fbw_picking['warehouse_number']
        storage_type = global_data.fbw_picking['storage_type']
        general_picking_operate(warehouse_number, storage_type, location_type=75)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','OOS')
    def test_normal_picking_oos(self):
        """
        【114910】Normal Picking OOS流程
        """
        warehouse_number = global_data.normal_picking['warehouse_number']
        storage_type = global_data.normal_picking['storage_type']
        general_picking_operate(warehouse_number, storage_type, is_oos=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'OOS','MO')
    def test_mo_picking_oos(self):
        """
        【115012】MO Picking OOS流程
        """
        warehouse_number = global_data.mo_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=50, picking_channel=1, module_name="mail_order",is_oos = True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'OOS','MOF')
    def test_mof_picking_oos(self):
        """
        【115222】MOF Picking OOS流程
        """
        warehouse_number = global_data.mof_picking['warehouse_number']
        general_picking_operate(warehouse_number, "6", location_type=50, picking_channel=1, module_name="mail_order", is_mof=True,is_oos = True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_oneitem_oos(self):
        """
        【114912】Oneitem oos流程
        """
        warehouse_number = global_data.one_item_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=52, is_oneitem=True,is_oos=True)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','bulk')
    def test_bulk_oos(self):
        """
        【114911】Bulk oos流程
        """
        warehouse_number = global_data.bulk_picking['warehouse_number']
        general_picking_operate(warehouse_number, "1", location_type=73, is_bulk=True,is_oos=True)

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','bulk')
    # def test_normal_packing(self):
    #     """
    #     【112823】普通订单打包出库流程
    #     """
    #     for order_type in self.order_type_list:
    #         storage_type = wms.select_order_storage_type(order_type)
    #         order_list = wms.batch.query_order_pick_type(self.warehouse_number, self.region_name, self.delivery_date, "10", self.delivery_id, pick_type=1, order_type=order_type)
    #         for order in order_list:
    #             if order["shipping_status"] in (50,51,60,61):
    #                 packing_process.general_packing_operate(self.warehouse_number, order["tote_no"], storage_type)

    # @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS','bulk')
    # def test_bulk_packing(self):
    #     """
    #     【112835】Bulk打包出库流程
    #     """
    #     for order_type in self.order_type_list:
    #         storage_type = wms.select_order_storage_type(order_type)
    #         order_list = wms.batch.query_order_pick_type(self.warehouse_number, self.region_name, self.delivery_date, "10", self.delivery_id, pick_type=4, order_type=order_type)
    #         for order in order_list:
    #             if order["shipping_status"] in (50,51,60,61):
    #                 packing_process.bulk_packing_operate(self.warehouse_number, order["tote_no"], storage_type)

    def teardown_class(self):
        # 退出登录
        wms.logout(warehouse_number=self.warehouse_number, user_id=self.user_id, user_name=self.user_name)
        


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
    # 只运行包含mark:api的case
    # weeeTest.main(ext=["-m","bulk"])

