# !/usr/bin/python3
# -*- coding: utf-8 -*-
import json
import weeeTest
from weeeTest import jmespath
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api.wms.adjust.adjust_api import Adjust
from wms.test_dir.business.adjust_approve_process import AdjustApproveProcess


class TestCentralAdjust(weeeTest.TestCase):
    def setup_class(self):
        self.warehouse_number = global_data.adjust['warehouse_number']
        self.item_number = global_data.adjust['item_number']
        self.bin_location = global_data.adjust['bin_location']
        self.stock_location = global_data.adjust['stock_location']
        self.storage_type = global_data.adjust['storage_type']
        self.expire_dtm = global_data.adjust['expire_dtm']

        self.user_id, self.user_name = wms.wms_login.common_login()
        self.in_user = self.user_name + '(' + self.user_id + ")"
        approval_limit_config = json.loads(wms.get_central_config(self.warehouse_number, "wms:approval:limit"))
        # 小于阈值不用进审核
        self.approval_limit = approval_limit_config["approval_limit"][self.storage_type] - 1

    def bin_adjust(self, action="add", need_approve=False, approve_type=1):
        in_user = self.user_name + '(' + self.user_id + ")"
        lpn_no = None
        inv_res = wms.adjust_api.query_location_inv(self.item_number, self.bin_location, self.warehouse_number, lpn_no)["body"]["invAdjustList"]
        # 参数设置
        is_lpn = False
        pre_qty = 0
        pieces_per_pack = "10.00"
        adjust_number = min(self.approval_limit, int(float(pieces_per_pack)))
        if need_approve:
            adjust_number = self.approval_limit + 1
        if action == "reduce":
            adjust_number = -adjust_number
        batch_no = None
        lpn_list = []
        if inv_res:
            self.expire_dtm = inv_res[0]["expire_dtm"]
            pieces_per_pack = inv_res[0]["pieces_per_pack"]
            batch_no = inv_res[0]["batch_no"]
            pre_qty = inv_res[0]["quantity"]

        wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, in_user, self.bin_location, adjust_number, is_lpn, pieces_per_pack,
                                        self.expire_dtm, batch_no, lpn_list)
        inv_res = wms.adjust_api.query_location_inv(self.item_number, self.bin_location, self.warehouse_number, lpn_no)["body"]["invAdjustList"]
        if need_approve:
            AdjustApproveProcess().inv_approve_operate(self.in_user, self.item_number, self.warehouse_number, self.bin_location, 2, approve_type)
        else:
            assert inv_res, f"仓库：{self.warehouse_number}，Item_number:{self.item_number}给库位{self.bin_location}添加库存后，查询不到数据"
            if batch_no:
                post_qty = jmespath(inv_res, f"[?batch_no=='{batch_no}'].quantity")[0]
            else:
                post_qty = jmespath(inv_res, f"[?expire_dtm=='{self.expire_dtm}'].quantity")[0]
            assert post_qty == pre_qty + adjust_number, f"仓库：{self.warehouse_number}，Item_number:{self.item_number}给库位{self.bin_location}添加库存后，库存应为{pre_qty + adjust_number}，实际为{post_qty}"

    def bin_lock(self, is_lock=1):
        # 参数设置
        is_lpn = False
        lpn_nos = None
        # 查询batch_no信息
        resp1 = wms.adjust_api.query_location_inv(self.item_number, self.bin_location, self.warehouse_number, lpn_no=lpn_nos)["body"]["invAdjustList"]
        if not resp1:
            return
        batch_no = resp1[0]["batch_no"]
        wms.adjust_api.lock_inv(is_lock, self.item_number, self.bin_location, self.warehouse_number, is_lpn, lpn_nos, batch_no)

    def lpn_lock(self):
        inv_res = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number)["body"]["invAdjustList"]
        lpn_no = jmespath(inv_res, "[0].lpnInfos[0].lpn_no")
        batch_no = jmespath(inv_res, "[0].lpnInfos[0].batch_no")
        is_lock = 1  # LPN加锁
        is_lpn = True
        wms.adjust_api.lock_inv(is_lock, self.item_number, self.stock_location, self.warehouse_number, is_lpn, [lpn_no], batch_no)
        is_lock = 0  # LPN解锁
        wms.adjust_api.lock_inv(is_lock, self.item_number, self.stock_location, self.warehouse_number, is_lpn, [lpn_no], batch_no)

    # 非LPN库存查询调整
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_query_inv_upc(self):
        """
        【111207】Adjust:非LPN库存查询
        """
        lpn_no = None
        wms.adjust_api.query_location_inv(self.item_number, self.bin_location, self.warehouse_number, lpn_no)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_upc_in(self):

        """
        【111216】Adjust：非LPN库存调增
        """
        self.bin_adjust()

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_upc_out(self):
        """
        【112892】Adjust：非LPN库存调减
        """
        self.bin_adjust(action="reduce")

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_bin_approve_pass(self):
        """
        【114917】Adjust:非LPN库存调增进审核且审核通过
        """
        self.bin_adjust(need_approve=True, approve_type=1)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_bin_approve_recount(self):
        """
        【114918】Adjust:非LPN库存调增进审核且审核拒绝
        """
        self.bin_adjust(need_approve=True, approve_type=0)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_lock_upc(self):
        """
        【111218】Adjust:Lock非LPN库存
        """
        self.bin_lock()

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_unlock_upc(self):
        """
        【111220】Adjust:unLock非LPN库存
        """
        self.bin_lock(is_lock=0)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_expire_dtm_upc(self):
        """
        【111222】Adjust:修改非LPN保质期
        """
        lpn_nos = None
        is_all_lpn = None
        # 查询batch_no信息
        resp = wms.adjust_api.query_location_inv(self.item_number, self.bin_location, self.warehouse_number, lpn_no=lpn_nos)["body"]["invAdjustList"]
        if not resp:
            return
        batch_no = resp[0]["batch_no"]
        batch_inv_infos = [
            {
                "location_no": self.bin_location,
                "batch_no": batch_no
            }]
        # 设置当前日期为过期时间
        new_expire_date = wms.util.get_special_date()
        wms.adjust_api.modify_shelf_life(self.warehouse_number, self.item_number, [self.bin_location], lpn_nos, is_all_lpn, batch_inv_infos, new_expire_date,
                                         new_expire_date)

    # LPN库存查询调整
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_query_inv_lpn(self):
        """
        【111215】Adjust：LPN库存查询
        """
        inv_res1 = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number)["body"]["invAdjustList"]
        if not inv_res1:
            return
        lpn_no = jmespath(inv_res1, "[0].lpnInfos[0].lpn_no")
        wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number, lpn_no)

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_lpn_in(self):
        """
        【111217,112891】Adjust：LPN库存调减调增
        """
        # 参数设置
        adjust_number = 1
        is_lpn = True
        pieces_per_pack = "10.00"
        inv_res2 = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number)["body"]["invAdjustList"]
        if not inv_res2:
            expire_dtm = wms.util.get_special_date(days=3650)
            receive_dtm = wms.util.get_special_date(days=0)
            wms.adjust_api.create_batch_inv(
                self.item_number,
                self.warehouse_number,
                self.in_user,
                self.stock_location,
                min(self.approval_limit, int(float(pieces_per_pack))),
                is_lpn=True,
                pieces_per_pack=pieces_per_pack,
                expire_dtm=expire_dtm,
                receive_date=receive_dtm,
                lpn_qty=1
            )
            inv_res2 = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number)["body"]["invAdjustList"]
        location_qty = jmespath(inv_res2, "[0].quantity")
        lpn_no = jmespath(inv_res2, "[0].lpnInfos[0].lpn_no")
        batch_no = jmespath(inv_res2, "[0].lpnInfos[0].batch_no")
        expire_dtm = jmespath(inv_res2, "[0].lpnInfos[0].expire_dtm")
        pieces_per_pack = jmespath(inv_res2, "[0].lpnInfos[0].pieces_per_pack")
        pre_qty = jmespath(inv_res2, "[0].lpnInfos[0].quantity")

        # LPN 调减
        lpn_list = [
            {
                "lpn_no": lpn_no,
                "original_quantity": pre_qty,
                "adjust_number": -adjust_number
            }
        ]
        wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, self.in_user, self.stock_location, -adjust_number, is_lpn, pieces_per_pack,
                                        expire_dtm, batch_no, lpn_list)

        # LPN 调增
        lpn_list = [
            {
                "lpn_no": lpn_no,
                "original_quantity": pre_qty - adjust_number,
                "adjust_number": adjust_number
            }
        ]
        wms.adjust_api.create_batch_inv(self.item_number, self.warehouse_number, self.in_user, self.stock_location, adjust_number, is_lpn, pieces_per_pack,
                                        expire_dtm, batch_no, lpn_list)

        inv_res3 = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number, lpn_no)["body"]["invAdjustList"]
        assert location_qty == jmespath(inv_res3, f"[?batch_no=='{batch_no}'].quantity")[0], "库存调整后与预期结果不一致"

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_lock_lpn(self):
        """
        【111219,111221】Adjust:Lock/unLock LPN库存
        """
        self.lpn_lock()

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS', 'WMS-PRD')
    def test_adjust_expire_dtm_lpn(self):
        """
        【111223】Adjust:修改LPN保质期
        """
        # 参数设置
        location_nos = None
        is_all_lpn = False
        inv_res4 = wms.adjust_api.query_location_inv(self.item_number, self.stock_location, self.warehouse_number)["body"]["invAdjustList"]
        lpn_no = jmespath(inv_res4, "[0].lpnInfos[0].lpn_no")
        batch_no = jmespath(inv_res4, "[0].lpnInfos[0].batch_no")
        batch_inv_infos = [
            {
                "location_no": self.stock_location,
                "batch_no": batch_no
            }]
        # 设置当前日期为过期时间
        new_expire_date = wms.util.get_special_date()
        wms.adjust_api.modify_shelf_life(self.warehouse_number, self.item_number, location_nos, [lpn_no], is_all_lpn, batch_inv_infos, new_expire_date,
                                         new_expire_date)

    def teardown_class(self):
        # 退出登录
        wms.logout(warehouse_number=self.warehouse_number, user_id=self.user_id, user_name=self.user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=True)
