import weeeTest
from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.simple.cycle_count.simple_count_api import CountApi


class TestCountStock(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_stock_count(self):
        """
        【112888】Stock盘点:领取任务+盘点全流程
        """
        # 登录
        warehouse_number = global_data.get_count_stock['warehouse_number']
        user_id, user_name = wms.wms_login.common_login()
        edit_user = user_name + '(' + user_id + ")"  # 拼接in_user
        in_user = edit_user
        # 参数获取
        storage_type = global_data.get_count_stock['storage_type']
        location_type = global_data.get_count_stock['location_type']
        status = global_data.get_count_stock['status']
        module_name = global_data.get_count_stock['module_name']
        diff_qty = 1  # 差异值
        weeeTest.log.info('盘点开始............................')
        # 调用getList4Count领取任务
        CountApi().get_stock_task(edit_user)

        count_data = CountApi().get_count_data(warehouse_number, edit_user, storage_type, location_type, status, module_name)
        count_id = count_data[0]
        location_no = count_data[1]
        status = count_data[2]
        sql_s = status  # 根据盘点状态进行盘点
        # 盘点次数循环：1/2/3
        while sql_s < 3:
            detail = CountApi().get_location_detail(count_id, status, module_name)
            sku_list = detail[0]
            resp1 = detail[1]
            # SKU个数获取
            if sku_list > 0:
                sku_data = CountApi().get_sku_detail(resp1, location_no, warehouse_number)
                item_list = sku_data[0]
                is_lpn_list = sku_data[1]
                for i in range(len(item_list)):
                    item_number = item_list[i]
                    is_lpn = is_lpn_list[i]
                    # 判断是LPN盘点还是UPC盘点
                    if is_lpn is None:
                        # UPC 盘点
                        CountApi().count_bin_upc(item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name)
                    else:
                        # LPN 盘点
                        CountApi().count_stock_lpn(is_lpn, warehouse_number, location_no, item_number, in_user, status, module_name)
                # 盘点finish
                CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
                # 盘点结果检查
                sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)
            else:
                # 没有SKU,走empty流程
                CountApi().count_empty(count_id, in_user, status, module_name)
                # 盘点结果检查
                sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)

        # 退出登录
        wms.wms_login.logout(warehouse_number, user_id, user_name)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
