import weeeTest
from weeeTest import jmespath
from wms.test_dir.api.wms.wms import wms
from wms.test_dir.api_case.wms.simple.cycle_count.simple_count_api import CountApi


class TestCountScanLocation(weeeTest.TestCase):
    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_scan_location_count_create(self):
        """
        【111603】ScanLocation盘点：创建任务并盘点
        """
        # 登录
        warehouse_number = 48
        user_id, user_name = wms.wms_login.common_login()
        in_user = user_name + '(' + user_id + ")"  # 拼接in_user
        # 开始盘点
        weeeTest.log.info('盘点开始............................')
        # 设置参数
        location_no = "C0212-2-2"
        diff_qty = 1
        module_name = 'scan_location_cycle_count'
        # 更新盘点数据
        wms.wms_db.update_count_info(warehouse_number, location_no)
        # 查询库位盘点任务状态
        resp = wms.count.query_location_count_status(location_no, in_user, warehouse_number)
        count_id = jmespath(resp, "body.count_id")
        status = jmespath(resp, "body.status")
        # 判断任务是否被其他人领取
        if status != 6:
            # 判断是否有任务
            if count_id is None:
                # 没任务,生成新任务
                resp_new = wms.count.create_count_task_by_location(location_no, warehouse_number, in_user)
                count_id = jmespath(resp_new, "body.count_id")
                status = jmespath(resp_new, "body.status")
                wms.count.scan_location(count_id, status, module_name)
                # 查询库位SKU_number
                sku_length = wms.wms_db.get_sku_from_wh_inventory_transaction(warehouse_number, location_no)
                # sku_length输出：[{'item_number': '15597'}, {'item_number': '98817'}]
                sku_list = len(sku_length)  # 库位SKU的个数,循环个数finish的时候参数需要
                weeeTest.log.info(f'库位SKU的个数：{sku_list}个')
                sql_s = status  # 根据盘点状态进行盘点
                # 盘点次数循环：1/2/3
                while sql_s < 3:
                    # for循环盘点SKU
                    if sku_list > 0:
                        # 根据库位SKU个数拼接item_numbers
                        data_list = sku_length
                        item_numbers = []
                        for data in data_list:
                            item_number = data["item_number"]
                            item_numbers.append(item_number)
                        weeeTest.log.info(f'库位SKU:{item_numbers}')
                        for item in item_numbers:
                            item_number = item
                            rec_id = count_id
                            resp2 = wms.count.goods(location_no, warehouse_number, item_number, rec_id)
                            is_lpn = jmespath(resp2, "body.lpnList")
                            if is_lpn is None:
                                # UPC盘点
                                CountApi().count_bin_upc(item_number, warehouse_number, location_no, diff_qty, in_user, status, module_name)
                            else:
                                # LPN盘点
                                CountApi().count_stock_lpn(is_lpn, warehouse_number, location_no, item_number, in_user, status,
                                                           module_name)
                        # 盘点finish
                        CountApi().count_finish(count_id, in_user, sku_list, module_name, status)
                        # 盘点结果检查
                        sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)

                    else:
                        # 没有SKU,走empty流程
                        CountApi().count_empty(count_id, in_user, status, module_name)
                        # 盘点结果检查
                        sql_s, status = CountApi().count_checks(count_id, status, user_id, warehouse_number)
                weeeTest.log.info(f'新生成的盘点任务已盘点完成......')
            else:
                weeeTest.log.info(f'盘点任务已存在......')
        else:
            # 盘点任务审核中,status=6
            weeeTest.log.info(f'盘点任务待审核......')
        # 退出登录
        wms.logout(user_id=user_id, user_name=user_name, warehouse_number=warehouse_number)
        weeeTest.log.info(f'已退出登录......')


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
