# !/usr/bin/python3
# -*- coding: utf-8 -*-

import weeeTest
from weeeTest import jmespath

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms


class TestBulkPacking(weeeTest.TestCase):

    @weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_bulk_packing(self):
        """
        【112835】Bulk打包出库流程
        """
        warehouse = global_data.bulk_packing['warehouse']
        cart_no = global_data.bulk_packing['cart_no']
        order_id = global_data.bulk_packing['order_id']
        storage_type = global_data.bulk_packing['storage_type']
        wms.wms_db.rollback_wait_packing(warehouse, order_id, cart_no)

        # 登录
        account, username = wms.wms_login.common_login()
        # 设置仓库
        wms.util.update_header(weee_warehouse=str(warehouse))
        # 设置storage_type
        wms.util.update_header(weee_wms_storage_type=str(storage_type))
        # check batch
        is_batch = wms.get_inventory_batch_switch(warehouse, storage_type)
        # 获取操作时间
        in_dtm = wms.util.get_current_time()
        # 开始打包
        wms.bulk_packing.user = username + '(' + account + ')'
        wms.bulk_packing.warehouse = warehouse
        # 获取打包任务类型,更新header
        resp = wms.bulk_packing.query_packing_task(cart_no)
        wms.util.update_header(weee_wms_packing_type=str(jmespath(resp, "body.packing_type")))
        items = wms.bulk_packing.query_bulk_packing_info(cart_no)["items"]

        # 当打包Tote/cart中库存数据异常时，恢复库存
        if wms.adjust_api.query_location_inv_list(warehouse, cart_no) == []:
            for item in items:
                if isinstance(item, dict):
                    wms.adjust_api.create_batch_inv(item["item_number"], warehouse, username + '(' + account + ')',
                                                    cart_no, item["item_quantity"])
                if is_batch:
                    item["batch_no"] = wms.wms_db.get_batch_inventory_transaction(warehouse, cart_no, item["item_number"])["batch_no"]

        wms.bulk_packing.bulk_packing_qc(cart_no, items[0])
        for order in order_id:
            # 校验订单状态(shipping_status=60)
            wms.wms_db.check_shipping_status(order_id=order,status=60)
            # 检查订单下商品是否全部QC
            wms.wms_db.check_all_qc(order_id=order)
        wms.bulk_packing.bulk_complete(cart_no, items)
        wms.bulk_packing.bulk_label_creates(cart_no, items[0])
        wms.bulk_packing.bulk_ship(cart_no)
        for order in order_id:
            # check order status
            wms.wms_db.check_shipping_status(order_id=order,status=70)
        # check tote status
        wms.wms_db.check_tote_status(warehouse=warehouse,tote_no=cart_no,status=0)
        # check inventory
        wms.wms_assert.check_location_empty(warehouse, cart_no)
        # check inventory 上报
        for order in order_id:
            for item in items:
                wms.wms_assert.check_non_batch_invenotry(warehouse, cart_no, item["item_number"],
                                                         -item["quantity"],
                                                         reference_no=order,
                                                         inventory_type=[261], in_dtm=in_dtm)
                if is_batch:
                    wms.wms_assert.check_batch_invenotry(warehouse, cart_no, item["item_number"],
                                                         -item["quantity"],
                                                         batch_no=item["batch_no"],
                                                         reference_no=order, inventory_type=[261], in_dtm=in_dtm)
        #check report TMS tracking number
        for order in order_id:
            order_info = wms.wms_db.select_wms_order_info(order)
            wms.wms_assert.check_hook_report(order_info["source_order_id"], "TMS_PACKAGE", in_dtm)

        # 退出登录
        wms.logout(warehouse_number=warehouse, user_id=account, user_name=username)
        # 将cart的状态置为占用，避免被其他业务使用
        wms.wms_db.update_location_status(warehouse, cart_no, 3)


if __name__ == '__main__':
    weeeTest.main(base_url="https://api.tb1.sayweee.net", debug=True, open=False)
