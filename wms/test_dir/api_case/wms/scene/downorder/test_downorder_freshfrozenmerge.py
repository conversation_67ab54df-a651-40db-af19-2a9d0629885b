# !/usr/bin/python3
# -*- coding: utf-8 -*-
"""
<AUTHOR>  Author
@Version        :  V1.0.0
------------------------------------
@File           :  test_downorder_bulk.py
@Description    :
@CreateTime     :  2025/2/17 18:38
@Software       :  PyCharm
------------------------------------
@ModifyTime     :  2025/2/17 18:38
"""

import time
import weeeTest

from wms.qa_config import global_data
from wms.test_dir.api.wms.wms import wms
from tms.test_dir.api.tms.tms import tms

class TestDownOrder_freshfrozenmerge(weeeTest.TestCase):
    """
    订单下发仓库测试
    """

    #@weeeTest.mark.list('WMS-Regression', 'WMS-Smoke', 'WMS')
    def test_fresh_frozen_down_order(self,order_id = None):
        """
        【107303】FreshFrozen合单下发测试
        """
        warehouse_number = global_data.alcohol_picking['warehouse_number']
        # 登录
        wms.wms_login.common_login()

        if order_id is None:
            order_id = 42643348  # 默认订单ID

        result = wms.wms_db.get_so_order_mapping(order_id=order_id, info=['woi.delivery_id'])
        if result is not None:
            delivery_id = result['delivery_id']

            # 重置数据并下发
            tms.login()
            wms.reset_data(delivery_id)
            # 查询订单Frozen SKU
            result1 = tms.tms_db.query_order_sku(order_id=order_id, storage_type="F")
            item_number = result1[0][0]

            #查询down单模型，确认支持FreshFrozen Merge
            wms.common_api.update_sys_config(config_id=1272,config_key="wmsso:downorder:model",warehouse_number=warehouse_number,
                                             config_value='{ "model": "all", "freshFrozenMerge": true, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }')

            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            # check order_type
            order_message = wms.wms_db.get_order_type(so_order_id=order_id, item_number=item_number,
                                                      info=['woi.order_type'])
            order_type = order_message.get('order_type')
            if order_type == 2:
                print("FreshFrozen合单成功")
            else:
                print("FreshFrozen合单失败")
            wms.check_data(delivery_id_new)
        else:
            # 直接下发

            # 查询订单Frozen SKU
            result1 = tms.tms_db.query_order_sku(order_id=order_id, storage_type="F")
            item_number = result1[0][0]
            #查询down单模型，确认支持FreshFrozen Merge
            wms.common_api.update_sys_config(config_id=1272,config_key="wmsso:downorder:model",warehouse_number=warehouse_number,
                                             config_value='{ "model": "all", "freshFrozenMerge": true, "dryIceSplit": true, "dryBulkSplit": true, "freshBulkSplit": true, "fbwRestaurantSplit": true }')

            wms.down_order_process(order_id)
            delivery_id_new = wms.get_delivery_id(order_id)
            # check order_type
            order_message = wms.wms_db.get_order_type(so_order_id=order_id, item_number=item_number,
                                                      info=['woi.order_type'])
            order_type = order_message.get('order_type')
            if order_type == 2:
                print("FreshFrozen合单成功")
            else:
                print("FreshFrozen合单失败")
            wms.check_data(delivery_id_new)


if __name__ == '__main__':
    weeeTest.main(base_url='https://api.tb1.sayweee.net', debug=True)
